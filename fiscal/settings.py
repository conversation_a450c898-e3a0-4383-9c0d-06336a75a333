"""
Django settings for fiscal project.

Generated by 'django-admin startproject' using Django 2.1.7.

For more information on this file, see
https://docs.djangoproject.com/en/2.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.1/ref/settings/
"""

import os

# Project Config
URL_IBGE = os.environ.get('URL_IBGE')
URL_TOTVS = os.environ.get('URL_TOTVS')
URL_IDWALL = os.environ.get('URL_IDWALL')
TOKEN_IDWALL = os.environ.get('TOKEN_IDWALL')


# URL_IBGE = "https://servicodados.ibge.gov.br/api/v1/localidades/"
# URL_TOTVS = "http://effsolucoeseassessor.protheus.cloudtotvs.com.br:8090/rest/"
# # URL_TOTVS = "http://effsolucoeseassessor4316.protheus.cloudtotvs.com.br:8090/rest/"
# URL_IDWALL = "https://api-v2.idwall.co"
# TOKEN_IDWALL = "1ea25f60-02e7-4676-9588-c28d0d9a1795"

# Static Config
S3_BUCKET = os.environ.get('S3_BUCKET')
AWS_S3_REGION_NAME = os.environ.get('AWS_S3_REGION_NAME')
AWS_ACCESS_KEY_ID = os.environ.get('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')


# AWS_ACCESS_KEY_ID = "********************"
# AWS_SECRET_ACCESS_KEY = "/lMP6k3z9Nfs8j18Snv0K0Vo+MISkaeIutWWI2Jc"
# S3_BUCKET = "swagger-static"

STATICFILES_STORAGE = "django_s3_storage.storage.StaticS3Storage"
AWS_STORAGE_BUCKET_NAME = S3_BUCKET
AWS_S3_BUCKET_NAME_STATIC = S3_BUCKET
AWS_S3_CUSTOM_DOMAIN = '%s.s3.us-west-2.amazonaws.com' % S3_BUCKET
STATIC_URL = "https://%s/" % AWS_S3_CUSTOM_DOMAIN
AWS_DEFAULT_ACL = False

# RDS_DB_NAME = "subadiqdb1"
# RDS_USERNAME = "subadiqdb"
# RDS_PASSWORD = "1o5gHogS"
# RDS_HOSTNAME = "subadiq-homolog.cxmg4sq9azs9.us-west-2.rds.amazonaws.com"

# RDS_DB_NAME = "subadiqdb1"
# RDS_USERNAME = "subadiqdb"
# RDS_PASSWORD = "SubAd1q32f"
# RDS_HOSTNAME = "transacsubadiq.cxmg4sq9azs9.us-west-2.rds.amazonaws.com"

RDS_DB_NAME = os.environ.get('RDS_DB_NAME')
RDS_USERNAME = os.environ.get('RDS_USERNAME')
RDS_PASSWORD = os.environ.get('RDS_PASSWORD')
RDS_HOSTNAME = os.environ.get('RDS_HOSTNAME')
OMIE_APP_KEY = os.environ.get('OMIE_APP_KEY')
OMIE_APP_SECRET = os.environ.get('OMIE_APP_SECRET')
OMIE_URL = os.environ.get('OMIE_URL')
CONTA_CORRENTE_OMIE = os.environ.get('CONTACORRENTEOMIE')

# Build paths inside the project like this= os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# SECURITY WARNING: keep the secret key used in production secret!
# SECRET_KEY = "7&!$rmu_0ls2-el(#om1n_7l2fib&-^m@xv#7h+paug0z^iy5*"
SECRET_KEY = os.environ.get('SECRET_KEY')


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
# SECRET_KEY = os.environ.get('SECRET_KEY')
# SECRET_KEY = '-e-n9rg4!!+!7=w#r6pzl+=7u3-9i4ncm)ihuftudlu&xk5a+q'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'api',
    'rest_framework',
    'rest_framework_swagger',
    'django_s3_storage',
    'corsheaders',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

CORS_ORIGIN_ALLOW_ALL = True

ROOT_URLCONF = 'fiscal.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'fiscal.wsgi.application'

# Database
# https://docs.djangoproject.com/en/2.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql_psycopg2',
        'NAME': RDS_DB_NAME,
        'USER': RDS_USERNAME,
        'PASSWORD': RDS_PASSWORD,
        'HOST': RDS_HOSTNAME,
        'PORT': 5432,
        'OPTIONS': {
            'options': '-c search_path=credenciamento'
        }
    }
}

# Password validation
# https://docs.djangoproject.com/en/2.1/ref/settings/#auth-password-validators
REST_FRAMEWORK = {
    "DEFAULT_SCHEMA_CLASS": "rest_framework.schemas.coreapi.AutoSchema",
}

# Internationalization
# https://docs.djangoproject.com/en/2.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_L10N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.1/howto/static-files/

# STATIC_URL = '/static/'
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
