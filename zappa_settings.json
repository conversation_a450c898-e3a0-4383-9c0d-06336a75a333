{"hml": {"aws_region": "us-west-2", "django_settings": "fiscal.settings", "project_name": "lucree-fiscal", "runtime": "python3.12", "s3_bucket": "fiscal-hml", "timeout_seconds": 600, "apigateway_description": "API fiscal da Lucree - HML", "lambda_description": "API fiscal da Lucree - HML", "memory_size": 256, "log_level": "DEBUG", "cors": true, "keep_warm": false, "domain": "api-hml.lucree.com.br", "base_path": "fiscal", "endpoint_configuration": ["REGIONAL"], "route53_enabled": false, "certificate_arn": "arn:aws:acm:us-east-1:175572419266:certificate/05725bab-8c5b-406f-a18d-cd26e4c9cfe8", "environment_variables": {"RDS_DB_NAME": "subadiqdb1", "RDS_USERNAME": "subadiqdb", "RDS_PASSWORD": "}6H:6;iGPd]", "RDS_HOSTNAME": "subadiq-homolog.cxmg4sq9azs9.us-west-2.rds.amazonaws.com", "AWS_ACCESS_KEY_ID": "********************", "AWS_SECRET_ACCESS_KEY": "CIaIYfcT70PzYIS4LCTRCnJwHT7+Bxg6LheI4+Oe", "S3_BUCKET": "swagger-static", "AWS_S3_REGION_NAME": "us-west-2", "LOG_LEVEL": "DEBUG", "URL_IBGE": "https://servicodados.ibge.gov.br/api/v1/localidades/", "URL_TOTVS": "http://effsolucoeseassessor4316.protheus.cloudtotvs.com.br:8090/rest/", "URL_IDWALL": "https://api-v2.idwall.co", "TOKEN_IDWALL": "1ea25f60-02e7-4676-9588-c28d0d9a1795", "SECRET_KEY": "-e-n9rg4!!+!7=w#r6pzl+=7u3-9i4ncm)ihuftudlu&xk5a+q", "API_CLIENTES_GATEWAY": "http://api-ecommerce-backoffice-develop-748562339.us-west-2.elb.amazonaws.com/bff/clients", "API_MENSAGERIA": "https://api.lucree.com.br/mensageria/", "EMAIL_TESTE": "<EMAIL>", "OMIE_URL": "https://app.omie.com.br/api/v1", "OMIE_APP_KEY": "38333295000", "OMIE_APP_SECRET": "4cea520a0e2a2ecdc267b75d3424a0ed", "CONTACORRENTEOMIE": "11850365", "LUCREE_API": "https://api-hml.lucree.com.br/", "LUCREE_API_AUTENTICACAO_USERNAME": "daniel.nascimento", "LUCREE_API_AUTENTICACAO_PASSWORD": "acessoLucree#2018"}, "exclude": ["*.md", "Dockerfile", ".*", "zappa_settings.json", "setup.cfg", "docker-*", "bitbucket-*", "ci.sh"], "vpc_config": {"SubnetIds": ["subnet-0de4da33e830fd6e8", "subnet-074eb9ab8523354aa"], "SecurityGroupIds": ["sg-01540ffbef59b0f51"]}}, "prod": {"extends": "hml", "environment_variables": {"RDS_DB_NAME": "subadiqdb1", "RDS_USERNAME": "subadiqdb", "RDS_PASSWORD": "7rW8dBYOEbC", "RDS_HOSTNAME": "transacsubadiq.cxmg4sq9azs9.us-west-2.rds.amazonaws.com", "AWS_ACCESS_KEY_ID": "********************", "AWS_SECRET_ACCESS_KEY": "CIaIYfcT70PzYIS4LCTRCnJwHT7+Bxg6LheI4+Oe", "S3_BUCKET": "swagger-static", "AWS_S3_REGION_NAME": "us-west-2", "LOG_LEVEL": "DEBUG", "URL_IBGE": "https://servicodados.ibge.gov.br/api/v1/localidades/", "URL_TOTVS": "http://effsolucoeseassessor.protheus.cloudtotvs.com.br:8090/rest/", "URL_IDWALL": "https://api-v2.idwall.co", "TOKEN_IDWALL": "1ea25f60-02e7-4676-9588-c28d0d9a1795", "SECRET_KEY": "-e-n9rg4!!+!7=w#r6pzl+=7u3-9i4ncm)ihuftudlu&xk5a+q", "API_CLIENTES_GATEWAY": "http://api-ecommerce-backoffice-master-2125968470.us-west-2.elb.amazonaws.com/bff/clients", "API_MENSAGERIA": "https://api.lucree.com.br/mensageria/", "OMIE_URL": "https://app.omie.com.br/api/v1", "OMIE_APP_KEY": "1844276155722", "OMIE_APP_SECRET": "ea2c0296966350cd4ebb4743934d039c", "CONTACORRENTEOMIE": "3785571704", "LUCREE_API": "https://api.lucree.com.br/", "LUCREE_API_AUTENTICACAO_USERNAME": "daniel.nascimento", "LUCREE_API_AUTENTICACAO_PASSWORD": "acessoLucree#2018"}, "lambda_description": "API fiscal da Lucree - Prod", "apigateway_description": "API fiscal da Lucree - Prod", "s3_bucket": "lucree-fiscal-prod", "domain": "api.lucree.com.br", "memory_size": 256, "timeout_seconds": 600, "endpoint_configuration": ["EDGE"], "keep_warm": true, "keep_warm_expression": "rate(5 minutes)", "vpc_config": {"SubnetIds": ["subnet-069b4cc8898ec7708", "subnet-01a2db17a3e4cc521", "subnet-0426a8959dc215581"], "SecurityGroupIds": ["sg-57861622"]}}}