from django.urls import path
from api import views


urlpatterns = [
    path('pedidos/permissao-faturamento/<int:lojista_id>', views.Pedidos.as_view({'get': 'permissaofaturamento'}), name='permissao-faturamento'),
    path('create-pedido/<int:lojista_id>/cliente', views.Clientes.as_view({'post': 'create'}), name='pedidos'),
    path('solicitacao/<str:solicitacao>/cliente/<int:lojista>', views.Clientes.as_view({'post': 'sol_utencilios'}), name='solicitacao'),
    path('negativa-pedido/<int:lojista_id>/cliente', views.Clientes.as_view({'post': 'pedido_negado'}), name='negativa de pedidos'),
    path('dirf/<str:documento>', views.Dirf.as_view({'get': 'get_dirf'}), name='get-dirf')
]
