from django.db import models
from rest_framework import serializers


class PessoaTipo(models.Model):
    
    descricao = models.CharField(max_length=200, help_text="Tipo de pessoa física/jurídica")

    class Meta:
        managed = False
        db_table = '"solicitacoes"."solicitacoes_pessoatipo"'


class PessoaTipoSerializers(serializers.ModelSerializer):
    class Meta:
        model = PessoaTipo
        fields = '__all__'


class Departamento(models.Model):
    
    descricao = models.CharField(max_length=200, help_text="Descrição do departamento.")

    class Meta:
        managed = False
        db_table = '"solicitacoes"."solicitacoes_departamento"'


class DepartamentoSerializers(serializers.ModelSerializer):
    class Meta:
        model = Departamento
        fields = '__all__'


class Pessoa(models.Model):
    
    nome = models.CharField(max_length=200, help_text="Nome da pessoa/Razão social da empresa")
    email = models.Char<PERSON>ield(max_length=200, help_text="E-mail da pessoa/empresa")
    cnpj_cpf = models.CharField(max_length=200, null=True, help_text="CPF/CNPJ da pessoa/empresa com pontuação")  # CPF ou CNPJ
    pessoa_tipo = models.ForeignKey(PessoaTipo, on_delete=models.CASCADE)
    departamento = models.ForeignKey(Departamento, on_delete=models.CASCADE)

    class Meta:
        managed = False
        db_table = '"solicitacoes"."solicitacoes_pessoa"'


class PessoaSerializers(serializers.ModelSerializer):
    class Meta:
        model = Pessoa
        fields = '__all__'


class Motivo(models.Model):
    
    descricao = models.CharField(max_length=200, help_text="Descrição do motivo")
    ativo = models.BooleanField(default=True)

    class Meta:
        managed = False
        db_table = '"solicitacoes"."solicitacoes_motivo"'


class MotivoSerializers(serializers.ModelSerializer):
    class Meta:
        model = Motivo
        fields = '__all__'



class Submotivo(models.Model):
    
    motivo = models.ForeignKey(Motivo, on_delete=models.CASCADE, null=True, related_name='submotivos')
    departamento = models.ForeignKey(Departamento, on_delete=models.CASCADE, null=True)
    descricao = models.CharField(max_length=200, help_text="Descrição do submotivo")
    ativo = models.BooleanField(default=True)
    encaminhar = models.CharField(max_length=200, default='')

    class Meta:
        managed = False
        db_table = '"solicitacoes"."solicitacoes_submotivo"'


class SubmotivoSerializers(serializers.ModelSerializer):
    class Meta:
        model = Submotivo
        fields = '__all__'


class Status(models.Model):
    
    descricao = models.CharField(max_length=200, help_text="Descrição do status")
    cor = models.CharField(max_length=200, help_text="Cor do status (bootstrap colors)", null=True)

    class Meta:
        managed = False
        db_table = '"solicitacoes"."solicitacoes_status"'


class StatusSerializers(serializers.ModelSerializer):
    class Meta:
        model = Status
        fields = '__all__'


class Solicitacao(models.Model):
    
    requerente = models.ForeignKey(Pessoa, on_delete=models.CASCADE, related_name='requerente', null=True, help_text='teste de parâmetro')
    submotivo = models.ForeignKey(Submotivo, on_delete=models.CASCADE)
    solicitante = models.ForeignKey(Pessoa, on_delete=models.CASCADE, related_name='solicitante')
    status = models.ForeignKey(Status, on_delete=models.CASCADE)
    departamento = models.ForeignKey(Departamento, on_delete=models.CASCADE, null=True)
    detalhamento = models.TextField(help_text="Detalhe da solicitação")
    numero_solicitacao = models.CharField(max_length=200, help_text="Enviar vazio, será preenchido automaticamente.")
    identificacao_chamado = models.CharField(max_length=200, help_text="Numero do chamado interno do integrador (sem efeito para a BEVIPag)")
    data_abertura = models.DateTimeField(auto_now_add=True, help_text="Data de cadastro (preenchido automaticamente)")
    data_fechamento = models.DateTimeField(null=True, help_text="Data de fechamento da solicitação (passar null se não for fechamento)")
    data_atualizacao = models.DateTimeField(auto_now=True, help_text="Data de última atualização (preenchido automaticamente)")
    etapa = models.IntegerField(null=True)

    class Meta:
        managed = False
        db_table = '"solicitacoes"."solicitacoes_solicitacao"'


class SolicitacaoSerializers(serializers.ModelSerializer):

    requerente = PessoaSerializers(read_only=True)
    submotivo = SubmotivoSerializers(read_only=True)
    solicitante = PessoaSerializers(read_only=True)
    status = StatusSerializers(read_only=True)
    departamento = DepartamentoSerializers(read_only=True)

    class Meta:
        model = Solicitacao
        fields = '__all__'


class CampoTipo(models.Model):
    
    descricao = models.CharField(max_length=200)
    
    class Meta:
        managed = False
        db_table = '"solicitacoes"."solicitacoes_campotipo"'


class CampoTipoSerializers(serializers.ModelSerializer):
    class Meta:
        model = CampoTipo
        fields = '__all__'


class Campo(models.Model):
    
    tipo = models.ForeignKey(CampoTipo, on_delete=models.CASCADE)
    solicitacao = models.ForeignKey(Solicitacao, on_delete=models.CASCADE, related_name='campos')
    descricao = models.CharField(max_length=200)
    data_cadastro = models.DateTimeField(auto_now_add=True)

    class Meta:
        managed = False
        db_table = '"solicitacoes"."solicitacoes_campo"'


class CampoSerializers(serializers.ModelSerializer):

    tipo = CampoTipoSerializers(read_only=True)

    class Meta:
        model = Campo
        fields = '__all__'
