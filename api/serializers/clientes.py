from rest_framework import serializers


class ClientesSerializers(serializers.Serializer):
    loja = serializers.Cha<PERSON><PERSON><PERSON>(max_length=200)
    nome = serializers.Char<PERSON><PERSON>(max_length=200)
    endereco = serializers.Char<PERSON><PERSON>(max_length=200)
    bairro = serializers.Char<PERSON>ield(max_length=200)
    municipio = serializers.Char<PERSON>ield(max_length=200)
    estado = serializers.Char<PERSON>ield(max_length=200)
    cod_municipio = serializers.Char<PERSON>ield(max_length=200)
    cep = serializers.Char<PERSON>ield(max_length=200)
    cnpj_cpf = serializers.Char<PERSON><PERSON>(max_length=200)
    inscricao_estadual = serializers.Char<PERSON><PERSON>(max_length=200)
    ddd = serializers.Char<PERSON><PERSON>(max_length=200)
    telefone = serializers.Char<PERSON><PERSON>(max_length=200)
    complemento = serializers.<PERSON><PERSON><PERSON><PERSON>(max_length=200)
    tipo_cliente = serializers.<PERSON><PERSON><PERSON><PERSON>(max_length=200)

    # def create(self, validated_data):
    #     pass

    class Meta:
        fields = (
            'loja',
            'nome',
            'endereco',
            'bairro',
            'municipio',
            'estado',
            'cod_municipio',
            'cep',
            'cnpj_cpf',
            'inscricao_estadual',
            'ddd',
            'telefone',
            'complemento',
            'tipo_cliente'
        )

