from api.models.general_models import AuthUser
from rest_framework.response import Response
from rest_framework import status, viewsets
from fiscal.log_service import Logger
from ..helpers.api_credeniamento import ApiCredenciamentoManager
from ..helpers.validadores import Validadores
from ..helpers.totvs import Totvs
from ..helpers.omie import Omie
from ..models import DashboardLojista, DashboardAuditoriaProtheus, DashboardAuditoriaOmie, DashboardProdutos
from ..models.solicitacoes_models import Solicitacao, Pessoa, Campo
import time
from django.db import IntegrityError
from django.db.models import Q
from pprint import pprint
from api.helpers.funcs import Funcs
from api.helpers.pendentes import pendentes
from os import environ as env
import logging
import requests
import json
import os

logger = Logger()
class Clientes(viewsets.ViewSet):

    """
    create:

    Método para chamada da inclusão de cadastros de cliente + pedidos no Totvs, este endpoint irá executar em cima
    de todos os cadastros que estão na fase de FATURAMENTO dentro do Integrador.

    """

    def create(self, request, **kwargs):

        try:
            lojista_id = kwargs['lojista_id']
            lojista = DashboardLojista.objects.filter(
                pk=lojista_id,
                status_idwall='CONCLUIDO',
                resultado_idwall='VALID'
            ).exclude(
                matriz_idwall__isnull=True,
                numero_idwall__isnull=True,
                nome=''
            ).first()
        except Exception as e:
            print(e)
            return Response({'erro': 'Lojista não informado!'}, status=status.HTTP_400_BAD_REQUEST)
        except KeyError:
            return Response({'erro': 'Lojista não informado!'}, status=status.HTTP_400_BAD_REQUEST)
        if not lojista:
            return Response({
                'erro': 'lojista não encontrado'
            }, status=status.HTTP_404_NOT_FOUND)

        try:
            auditoria_omie = DashboardAuditoriaOmie.objects.filter(
                lojista=lojista_id).first()
        except Exception as err:
            logger.registrar(f"Erro ao buscar auditoria do lojista: {err}", "ERROR")
            print(err)

        # Paulo Camacan LUC-618
        plano_fisico = hasattr(
            lojista, "plano") and lojista.plano and lojista.plano.plano_tipo_id == 1
        plano_virtual = hasattr(
            lojista, "plano_virtual") and lojista.plano_virtual and lojista.plano_virtual.plano_tipo_id == 2

        # Paulo Camacan LUC-680
        # se o lojista tiver flag (4) será negado para plano virtual
        elegivel: bool = (plano_virtual and not lojista.flag_link_pagto == 4)

        print("fisico: " + str(plano_fisico))
        print("virtual: " + str(plano_virtual))
        # Caso o lojista não tiver nennhum dos planos
        if not (plano_fisico or plano_virtual):
            return Response({
                'erro': 'lojista não tem nenhum plano'
            }, status=status.HTTP_404_NOT_FOUND)

        # se o lojista só tiver plano virtual e a flag (4) não pode avançar
        if not (plano_fisico or elegivel):
            return Response({
                'erro': 'lojista só tem o plano virtual e este está inelegível'
            }, status=400)

        # concatena o dicionario
        dados = {
            "cpf_cnpj": lojista.documento
        }

        if plano_fisico:
            try:
                print(request.data)
                dados["maquina"] = request.data["maquina"]
                dados["transportadora"] = request.data["transportadora"]
            except Exception as e:
                print("erro", e)
                return Response({
                    'erro': 'dados do transporte não informado'
                }, status=status.HTTP_404_NOT_FOUND)
        # end LUC-618

        validador = Validadores(dados)
        valido, resultado = validador.valida_cliente(dados)
        mensagens = []
        mensagem_pedido = None

        if not valido:
            try:
                DashboardAuditoriaOmie.objects.update_or_create(
                    lojista=lojista,
                    gerou_cliente=False,
                    gerou_pedido=False,
                    retorno_cliente=resultado
                )
            except IntegrityError as e:
                pprint(e)

        if auditoria_omie:
            cliente = auditoria_omie.num_omie_cli
            status_chamada = 200
            mensagem = "Cliente cadastrado com sucesso!"
        else:
            # totvs = Totvs(tipo='cliente')
            omie = Omie(tipo='cliente')
            # status_chamada, mensagem, cliente = totvs.chamadacliente(resultado)
            status_chamada, mensagem, cliente = omie.createCliente(resultado)

        if status_chamada == 201:
            pendentes.disable_pendente(lojista, "FISCAL")

        gerou_pedido = False
        chamada_pedido = None
        # Paulo Camacan LUC-618
        # flag para maq.virtual liberada

        # Gerar cliente na tabela do gateway
        # LUC-680
        # Link de pagamento negado
        if elegivel and Clientes.gerar_gateway_cliente(lojista):
            # dando timeout quando envia o email
            # desativada ate achar uma maneira de rodar em concorrente
            # Clientes.enviar_email_welcome(request, lojista)
            # ativa o plano_virtual
            lojista.flag_link_pagto = 3

        # so cria o pedido se tiver o plano fisico
        # so montar o pedido no plano fisico
        # logo o plano virtual também tem que passar
        if plano_fisico:
            valido_pedido, resultado_pedido = validador.valida_pedido(
                dados, cliente)

            if valido_pedido:
                gerou_pedido = True
                chamada_pedido = resultado_pedido
                print(resultado_pedido)
                omie_pedido = Omie(tipo='pedido')
                try:
                    status_chamada_pedido, mensagem_pedido, codigo_ped = omie_pedido.createPedido(
                        resultado_pedido)
                except Exception as err:
                    print(err)
                # totvs_pedido = Totvs(tipo='pedido')
                # status_chamada_pedido, mensagem_pedido, codigo_ped = totvs_pedido.chamadapedido(
                #    resultado_pedido)

                if status_chamada_pedido == 201:
                    lojista.numero_protheus = cliente
                    lojista.fase = Funcs.atualiza_fase(json.loads(lojista.fase), [
                        'Em aprovação comercial'], 'Faturamento')
                    lojista.fase_integracao = 'FATURAMENTO'
                    pendentes.disable_pendente(lojista, "FISCAL")
                elif status_chamada_pedido == 400:
                    return Response({
                        'erro': mensagem_pedido
                    }, status=status.HTTP_400_BAD_REQUEST)
                else:
                    pendentes.send_pendente(
                        lojista, "FISCAL", "pedido", mensagem_pedido)
            else:
                return Response({
                    'erro': 'maquina/cliente nao encontrado'
                }, status=status.HTTP_404_NOT_FOUND)
        # se somente for plano virtual, passa de fase
        else:
            mensagem_pedido = "Sem pedido, o cliente só tem maquina virtual"
            codigo_ped = None

            # atualiza a fase
            lojista.numero_protheus = cliente
            lojista.fase = Funcs.atualiza_fase(json.loads(lojista.fase), [
                "Em aprovação comercial",
                "Faturamento",
                "Preparação da máquina",
                "Expedição",
                "Máquina enviada"], "Máquina entregue")
            lojista.fase = Funcs.desabilita_fase(lojista.fase, [
                "Faturamento pendente"
            ])
            lojista.fase_integracao = 'ENTREGUES'
        lojista.save()
        """SCA 1613 Atualização de serviços externos"""
        #ApiCredenciamentoManager().atualizar_servico_externos(lojista_id)
        # END LUC-618
        try:
            created, obj = DashboardAuditoriaOmie.objects.update_or_create(
                lojista=lojista,
                gerou_cliente=True,
                gerou_pedido=gerou_pedido,
                chamada_cliente=resultado,
                num_omie_cli=cliente,
                num_omie_ped=codigo_ped,
                retorno_cliente=mensagem,
                chamada_pedido=chamada_pedido,
                retorno_pedido=mensagem_pedido,
                data_envio=time.strftime("%Y-%m-%d")
            )

            pprint(created)
            pprint(obj)

        except Exception as e:
            print(e)
            return Response({
                'erro': 'Erro inesperado'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        mensagens.append({"documento": {"etapa_1": mensagem,
                                        "etapa_2": mensagem_pedido, "cliente": cliente}})
        return Response({'msg': mensagens}, status=status_chamada)
    """
            else:
                pprint({'status': status_chamada, 'mensagem': mensagem})
                try:
                    # LUC-618
                    # so cria pedido se tiver plano_fisico
                    if plano_fisico:
                        self.cria_pedido_apartado(
                            lojista, validador, dados, cliente)

                except IntegrityError as e:
                    pprint(e)

                except DashboardAuditoriaOmie.MultipleObjectsReturned as e:
                    pprint('lojista: ' + str(lojista) +
                        ' retornou mais de um registro na tabela de auditoria')

                except DashboardAuditoriaOmie.DoesNotExist as e:
                    pprint('lojista: ' + str(lojista) +
                        ' não existe na tabela de auditoria')

            mensagens.append({"documento": {"etapa_1": mensagem,
                            "etapa_2": mensagem_pedido, "cliente": cliente}})
            return Response({'msg': mensagens}, status=status_chamada)
    """

    def pedido_negado(self, request, **kwargs):
        try:
            lojista_id = kwargs.get('lojista_id')
            lojista = DashboardLojista.objects.get(pk=lojista_id)
        except KeyError:
            return Response({
                'erro': 'Parametro não informado!'
            }, status=status.HTTP_400_BAD_REQUEST)
        except DashboardLojista.DoesNotExist:
            return Response({
                'erro': 'Lojista não encontrado com id: {}'.format(lojista_id)
            }, status=status.HTTP_404_NOT_FOUND)

        old_fases = json.loads(lojista.fase)
        lojista.fase = Funcs.atualiza_fase(
            old_fases, ['Em aprovação comercial'], 'Envio de máquina negado')
        lojista.fase_integracao = 'COMERCIAL'
        lojista.save()
        """SCA 1613 Atualização de serviços externos"""
        #ApiCredenciamentoManager().atualizar_servico_externos(lojista.id)
        return Response({}, status=status.HTTP_200_OK)

    def cria_pedido_apartado(self, lojista, validador, dados, cliente, utensilios=False, produtos=None):

        lojista.numero_protheus = cliente
        lojista.save()
        dados["maquina"] = produtos.descricao
        omie = DashboardAuditoriaOmie()
        omie.lojista = lojista
        omie.num_omie_cli = cliente
        omie.gerou_pedido = False

        if not utensilios:
            valido_pedido, resultado_pedido = validador.valida_pedido(
                dados, cliente)
        else:
            valido_pedido, resultado_pedido = validador.valida_pedido(
                dados, cliente)

        if valido_pedido:

            omie.chamada_pedido = resultado_pedido

            omie_pedido = Omie(tipo='pedido')
            status_chamada_pedido, mensagem_pedido, cod_pedido = omie_pedido.createPedido(
                resultado_pedido)

            if status_chamada_pedido == 201:
                omie.gerou_pedido = True
                omie.retorno_pedido = mensagem_pedido
                omie.num_omie_ped = cod_pedido
                pendentes.disable_pendente(lojista, "FISCAL")
            else:
                omie.retorno_pedido = {
                    'erro': 'Retorno da totvs não mapeado',
                    'status_chamada': status_chamada_pedido,
                    'mensagem': mensagem_pedido,
                    'num_protheus_cli': cliente}
                pendentes.send_pendente(
                    lojista, "FISCAL", "pedido", mensagem_pedido)

        omie.data_envio = time.strftime("%Y-%m-%d")
        omie.save()

    def sol_utencilios(self, request, **kwargs):

        try:
            solicitacao = kwargs['solicitacao']
            lojista = kwargs['lojista']
        except KeyError:
            return Response({'erro': 'Parametros não informados'})

        try:
            solicitacao = Solicitacao.objects.get(
                numero_solicitacao=solicitacao)
            lojista = DashboardLojista.objects.get(pk=lojista)
            omie = DashboardAuditoriaOmie.objects.filter(
                lojista=lojista).order_by('-id').first()

        except Solicitacao.DoesNotExist:
            return Response({
                'erro': 'solicitação com o identificador: {} não encontrado'.format(solicitacao)
            }, status=status.HTTP_404_NOT_FOUND)

        except DashboardLojista.DoesNotExist:
            return Response({
                'erro': 'lojista com o identificador: {} não encontrado'.format(lojista)
            }, status=status.HTTP_404_NOT_FOUND)

        if not omie:

            dados = {"cpf_cnpj": lojista.documento}
            validador = Validadores(dados)
            valido, resultado = validador.valida_cliente(dados)

            try:
                omie = DashboardAuditoriaOmie()
                omie.lojista = lojista
                omie.gerou_cliente = False
                omie.gerou_pedido = False

                chamadaOmie = Omie(tipo='cliente')
                status_chamada, mensagem, cliente = chamadaOmie.createCliente(
                    resultado)

                omie.chamada_cliente = resultado
                omie.num_omie_cli = cliente

                if status_chamada != 409:
                    omie.gerou_cliente = True
                    omie.retorno_cliente = mensagem
                    pendentes.disable_pendente(lojista, "FISCAL")
                else:
                    _resp = {
                        'erro': 'Retorno da omie não mapeado',
                        'status_chamada': status_chamada,
                        'mensagem': mensagem,
                        'num_omie_cli': cliente}
                    omie.retorno_cliente = _resp
                    pendentes.send_pendente(
                        lojista, "FISCAL", "pedidos", mensagem)
                    return Response(_resp, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                omie.data_envio = time.strftime("%Y-%m-%d")
                omie.save()

            except Exception:
                return Response({
                    'erro': 'Ocorreu um erro ao realizar chamada na totvs'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        cliente = omie.num_omie_cli
        dados = {"cpf_cnpj": lojista.documento, "transportadora": "02"}
        validador = Validadores(dados)

        if solicitacao.submotivo.id not in [20, 22]:
            # id 20 = BOBINA
            # id 22 = SOLICITAÇÃO DE TROCA DE CHIP

            return Response({
                'mensagem': 'Sub motivo não mapeado'
            }, status=status.HTTP_200_OK)

        if solicitacao.submotivo.id == 22:

            try:
                campo = Campo.objects.get(solicitacao=solicitacao, tipo_id=17)
                chip = 'CHIP {}'.format(str(campo.descricao).upper())
                produto = DashboardProdutos.objects.get(descricao=chip)

            except Campo.DoesNotExist:
                return Response({
                    'erro': 'Campo de confirmação de chip não informado na solicitação: {}'.format(solicitacao)
                }, status=status.HTTP_404_NOT_FOUND)
            except DashboardProdutos.DoesNotExist:
                return Response({
                    'erro': 'produto não encontrado com as descrições: {}'.format(','.join(chip))
                }, status=status.HTTP_404_NOT_FOUND)

        elif solicitacao.submotivo.id == 20:

            try:
                produto = DashboardProdutos.objects.get(descricao='BOBINA')
            except DashboardProdutos.DoesNotExist:
                return Response({
                    'erro': 'produto não encontrado com as descrições: {}'.format('BOBINA')
                }, status=status.HTTP_404_NOT_FOUND)

        self.cria_pedido_apartado(
            lojista, validador, dados, cliente, True, produto)
        """SCA 1613 Atualização de serviços externos"""
        #ApiCredenciamentoManager().atualizar_servico_externos(lojista_id=lojista.id)
        return Response(status=status.HTTP_204_NO_CONTENT)

    # LUC-680
    # Paulo Camacan, envia email de boas-vindas ao link de pagamento
    @classmethod
    def enviar_email_welcome(cls, request, lojista: DashboardLojista):
        auth_user = AuthUser(id=lojista.lojista_id)
        authorization = request.META.get(
            "HTTP_AUTHORIZATION", getattr(cls, "auth_token", None))

        if not authorization:
            resp = requests.post(
                "https://api.lucree.com.br/autenticacao/v1",
                json={
                    "username": "daniel.nascimento",
                    "password": "acessoLucree#2018"
                }
            )
            authorization = resp.json().get("id_token")
            setattr(cls, "auth_token", authorization)

        url: str = os.environ.get("API_MENSAGERIA") + "email/send"
        email = os.environ.get("EMAIL_TESTE", auth_user.email)
        try:
            resp = requests.post(url, json={
                "email_subject": "Bem-vindo a Máquina Virtual da Lucree!",
                "template": "welcome_link.html",
                "client_email": email,
                "email_data": {}
            }, headers={"Authorization": authorization})

            return resp.status_code == 200
        except Exception as error:
            logging.error(error)
        return False

    @classmethod
    def gerar_gateway_cliente(cls, lojista: DashboardLojista):
        # enviando cliente para a tabela gateway-pagamento.clients
        # Atualizado por: Daniel Nascimento - 22/04/2020
        payload = {
            'person_type': "COMPANY",
            'document_number': lojista.documento,
            'name': lojista.nome,
            'trade_name': lojista.nome,
            'local_auth_type': "HTTP_AUTHORIZATION_BASIC",
            'providers': "SOFTWARE_EXPRESS,PAYZEN",
            'generate_credential': 'true'
        }

        try:
            r = requests.post(env.get("API_CLIENTES_GATEWAY"), json=payload)
            return r.status_code in (200, 412)
        except Exception as e:
            # TODO: precisamos fazer uma auditoria disso
            print(str(e))
        return False
