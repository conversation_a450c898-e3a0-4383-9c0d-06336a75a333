from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework import status, viewsets
from rest_framework.views import APIView
from ..helpers.validadores import Validadores
from ..helpers.totvs import Totvs
from ..helpers.ibge import Ibge
from ..models import DashboardEstados, DashboardCidades


class ImportIbge(APIView):
    """
    get:
    Para atualização da nossa base do IBGE
    """

    @action(detail=False, methods=['get'])
    def get(self, request):
        ibge = Ibge()
        valid = ibge.get_ufs_ibge()
        estados = ibge.estados
        for estado in estados:
            est = DashboardEstados()
            est.id = estado["id"]
            est.sigla = estado["sigla"]
            est.nome = estado["nome"]
            est.save()
            ibge.get_cidades_por_estado_ibge(est.id)
            cidades = ibge.cidades
            for cidade in cidades:
                cid = DashboardCidades()
                cid.id = cidade["id"]
                cid.nome = cidade["nome"]
                cid.estado = est
                cid.save()






