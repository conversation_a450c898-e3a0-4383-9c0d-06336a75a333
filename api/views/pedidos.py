from rest_framework.response import Response
from ..models import DashboardLojista
from rest_framework import status, viewsets
from api.helpers.api_credeniamento import ApiCredenciamentoManager
from fiscal.log_service import Logger

logger = Logger()
class Pedidos(viewsets.ViewSet):

    def permissaofaturamento(self, request, lojista_id):
        try:
            lojista = DashboardLojista.objects.get(id=lojista_id)
            lojista.pode_faturar = True
            lojista.save()
            """Atualiza os serviços externos para o lojista especificado."""
            atualizador = ApiCredenciamentoManager
            atualizador.atualizar_servico_externos(lojista_id=lojista.id)
            return Response({'msg': 'Loja atualizada com sucesso!'}, status=status.HTTP_200_OK)

        except DashboardLojista.DoesNotExist:
            logger.registrar(f"Lojista não encontrado: {lojista_id}", "ERROR")
            return Response({'msg': 'Lojista não encontrado!'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.registrar(f"Erro ao atualizar lojista: {e}", "ERROR")
            return Response({'msg': 'Erro ao atualizar lojista!'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

