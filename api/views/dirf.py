from rest_framework.response import Response
from ..models import DashboardFiscalDirf
from rest_framework import status, viewsets
import json

class Dirf(viewsets.ViewSet):

    def get_dirf(self, request, documento):
        dirf = DashboardFiscalDirf.objects.filter(cnpj_tomador=documento).order_by('data_fato_gerado')
        count = dirf.count()
        if count:
            dict_dirf = {
                "nome_cliente": "",
                "cnpj_cliente": "",
                "dirfs": []
            }
            array_dirf = [

            ]
            for d in dirf:
                dict_dirf["nome_cliente"] = d.razao_social_tomador
                dict_dirf["cnpj_cliente"] = d.cnpj_tomador
                array_dirf.append({
                    'num_nf': d.num_nf,
                    'data_fato_gerado': d.data_fato_gerado,
                    'rendimentos': d.rendimentos,
                    'retido': d.retido
                })
                dict_dirf['dirfs'] = array_dirf

            return Response({'data': dict_dirf}, status=status.HTTP_200_OK)

        else:
            return Response({'msg': 'Dirf não encontrada'}, status=status.HTTP_404_NOT_FOUND)
