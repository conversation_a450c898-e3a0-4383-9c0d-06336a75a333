import requests
import json
from rest_framework import status
from django.conf import settings


class Totvs:
    def __init__(self, tipo):
        self.login = 'admin'
        self.senha = ' '

        # self.login = 'Admin'
        # self.senha = 'Admin@2019'
        if tipo == 'cliente':
            self.url = settings.URL_TOTVS + 'wsresteffcli/'
        else:
            self.url = settings.URL_TOTVS + 'wsresteffped/'

    def chamadacliente(self, body):
        headers = {'Content-Type': 'application/json'}
        results = None

        print(json.dumps(body))
        try:
            results = requests.post(self.url, auth=(
                self.login, self.senha), headers=headers, data=json.dumps(body))
        except Exception as e:
            print(str(e))

        cliente = None
        resultado_text = results.text
        try:
            valido, resultado = self.trataresultado(results.json())
            if valido is False:
                status_chamada = status.HTTP_409_CONFLICT
            else:
                status_chamada = status.HTTP_201_CREATED
                cliente = resultado['Cod_Retorno']
            mensagem = resultado['Mensagem']
        except TypeError:
            mensagem = resultado_text
            status_chamada = status.HTTP_400_BAD_REQUEST
            return status_chamada, mensagem, None
        except ValueError:
            mensagem = resultado_text
            status_chamada = status.HTTP_500_INTERNAL_SERVER_ERROR
            return status_chamada, mensagem, None

        return status_chamada, mensagem, cliente

    def chamadapedido(self, body):
        headers = {'Content-Type': 'application/json'}
        results = requests.post(self.url, auth=(
            self.login, self.senha), headers=headers, data=json.dumps(body))
        if results.status_code == 500:
            return status.HTTP_500_INTERNAL_SERVER_ERROR, 'Internal server error Protheus', None
        resultado_text = results.text
        try:
            valido, resultado = self.trataresultado(results.json())
            if valido is False:
                status_chamada = status.HTTP_409_CONFLICT
            else:
                status_chamada = status.HTTP_201_CREATED

            mensagem = resultado['Mensagem']
            codigo_ped = resultado.get('codigo', None)
            return status_chamada, mensagem, codigo_ped
        except ValueError:
            mensagem = resultado_text
            status_chamada = status.HTTP_500_INTERNAL_SERVER_ERROR
            return status_chamada, mensagem, None

    def trataresultado(self, resultadochamada):
        valido = False
        resultado = None

        if 'Status' in resultadochamada:
            if resultadochamada['Status'] != 'OK':
                if 'Mensagem' in resultadochamada:
                    resultado = resultadochamada
            else:
                valido = True
                resultado = resultadochamada
        return valido, resultado
