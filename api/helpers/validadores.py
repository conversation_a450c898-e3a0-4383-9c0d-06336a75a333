import logging
from api.helpers import MontaChamada
from ..models.general_models import (DashboardLojista,
                                     DashboardSintegraitem,
                                     DashboardEnderecolojista,
                                     DashboardResponsavel,
                                     DashboardPlanos,
                                     DashboardProdutos)
import unicodedata
from ..exceptions import IbgeExceptions
from string import punctuation
import requests
from ..exceptions.idwall_exceptions import IdwallExceptions
from ..exceptions.produtos_exceptions import ProdutosExceptions
from django.conf import settings
from django.core.exceptions import MultipleObjectsReturned


class Validadores:
    def __init__(self, body):
        self.body = body

    def valida(self, body):
        valid = False
        chaves_validas = self.valida_keys(body)
        if chaves_validas:
            valid = True

    def valida_cliente(self, body):
        chaves_validas = False
        try:
            lojista = self.checa_lojista(body['cpf_cnpj'])
            endereco = self.checa_endereco(lojista)
            responsavel = self.checa_responsavel(lojista)
            if all(n is not None for n in [lojista, endereco, responsavel]):
                montagem = MontaChamada(
                    body=body,
                    lojista=lojista,
                    endereco=endereco,
                    responsavel=responsavel,
                    validadores=self
                )
                chaves_resultados = montagem.montagem_clientes_omie()

                chaves_validas = True
            else:
                chaves_resultados = 'O CNPJ não tem os dados necessarios para criação de um cliente no Totvs (Verifique dados como: Endereço e Responsavel pela loja)'
                chaves_validas = False
            return chaves_validas, chaves_resultados
        except KeyError as e:
            chaves_resultados = "O campo: " + str(e) + " é obrigatório."
            return chaves_validas, chaves_resultados
        except IbgeExceptions as e:
            chaves_resultados = e
            return chaves_validas, chaves_resultados
        except IdwallExceptions as e:
            chaves_resultados = e
            return chaves_validas, chaves_resultados
        except ConnectionError as e:
            chaves_resultados = e
            return chaves_validas, chaves_resultados
        except ConnectionRefusedError as e:
            chaves_resultados = e
            return chaves_validas, chaves_resultados
        except ConnectionResetError as e:
            chaves_resultados = e
            return chaves_validas, chaves_resultados
        except Exception as error:
            logging.error(error)
        # except ProdutosExceptions as e:
        #     chaves_resultados = e
        #     return chaves_validas, chaves_resultados

    def valida_pedido(self, body, codigo_retorno, utensilios=False, produtos=None):
        chaves_validas = False
        try:
            lojista = self.checa_lojista(body['cpf_cnpj'])
            if lojista is not None:
                montagem = MontaChamada(
                    body=body, lojista=lojista, validadores=self)
                if not utensilios:
                    chaves_resultados = montagem.montagem_pedidos_omie(
                        codigo_retorno, transportadora=body['transportadora'])
                else:
                    chaves_resultados = montagem.montagem_pedidos_omie(
                        codigo_retorno, utensilios, produtos)
                chaves_validas = True
            else:
                chaves_resultados = 'O CNPJ não tem os dados necessarios para criação de um pedido no Totvs'
                chaves_validas = False
            return chaves_validas, chaves_resultados
        except KeyError as e:
            chaves_resultados = "O campo: " + str(e) + " é obrigatório."
            return chaves_validas, chaves_resultados
        except ProdutosExceptions as e:
            chaves_resultados = e
            return chaves_validas, chaves_resultados
        except Exception as e:
            chaves_validas = False
            chaves_resultados = "Produto invalido"
            return chaves_validas, chaves_resultados

    def checa_lojista(self, cpf_cnpj):
        lojista = None
        try:
            lojista = DashboardLojista.objects.get(documento=cpf_cnpj)
            if lojista.nome is not None and lojista.numero_idwall is not None:
                return lojista
            else:
                return None

        except DashboardLojista.DoesNotExist:
            return lojista

        except Exception as error:
            logging.error(error)

    def checa_endereco(self, lojista):
        endereco = None
        try:
            #endereco = DashboardEnderecolojista.objects.get(lojista=lojista, tipo='1')
            endereco = DashboardEnderecolojista.objects.filter(
                lojista=lojista, tipo='1').first()
            # except DashboardEnderecolojista.DoesNotExist:
            #    return endereco
        except Exception as e:
            print(str(e))
        return endereco

    def checa_responsavel(self, lojista):
        responsavel = None
        try:
            responsavel = DashboardResponsavel.objects.get(lojista=lojista)
            return responsavel
        except DashboardResponsavel.DoesNotExist:
            return responsavel
        except MultipleObjectsReturned:
            responsavel = DashboardResponsavel.objects.filter(
                lojista=lojista).first()
            return responsavel

    def checa_matriz(self, body):
        matriz_valida = False
        try:
            lojista = self.checa_lojista(body['cpf_cnpj'])
            if lojista is not None:
                if lojista.matriz_idwall is not None:
                    matriz_valida = True
                elif lojista.matriz_idwall is not None:
                    matriz_valida = True
            return matriz_valida
        except DashboardLojista.DoesNotExist:
            return matriz_valida

    def get_matriz(self, body):
        matriz = None
        if self.checa_matriz(body):
            lojista = self.checa_lojista(body['cpf_cnpj'])
            if lojista is not None:
                matriz = lojista.matriz_idwall
        return matriz

    def define_tipo(self, matriz):
        if matriz == 'lucree_QSA_pj':
            pessoa = 'J'
        else:
            pessoa = 'F'
        return pessoa

    def checa_isencao(self, cpf_cnpj):
        ie = None
        tipo_consumidor = None
        lojista = self.checa_lojista(cpf_cnpj)
        if lojista.matriz_idwall == 'lucree_QSA_pf':
            ie = 'ISENTO'
            tipo_consumidor = 'F'

        else:
            ie = self.checa_ie(lojista)
            if ie == 'ISENTO':
                tipo_consumidor = 'F'
            else:
                tipo_consumidor = 'R'

        return ie, tipo_consumidor

    def checa_ie(self, loja):
        ie = None
        try:
            sintegra = DashboardSintegraitem.objects.get(lojista=loja)
            if sintegra.inscricao_estadual is not None:
                ie = self.trata_ie(sintegra.inscricao_estadual)
            return ie
        except DashboardSintegraitem.DoesNotExist:
            cidwall = self.consulta_ie_idwall(loja.numero_idwall)
            if cidwall.status_code == 200:
                if 'sintegra' in cidwall.json()['result']:
                    try:
                        if cidwall.json()['result']['sintegra']['itens'][0]['inscricao_estadual'] is not None:
                            ie = self.trata_ie(
                                cidwall.json()['result']['sintegra']['itens'][0]['inscricao_estadual'])
                        else:
                            ie = 'ISENTO'
                    except IndexError:
                        ie = 'ERRO_IDWALL'
                else:
                    raise IdwallExceptions(
                        'Dados do sintegra/inscrição estadual não se encontram na IDWALL')
            elif cidwall.status_code == 401:
                raise IdwallExceptions('Falha de autenticação na IDWALL')
            elif cidwall.status_code == 404:
                raise IdwallExceptions('Relatório não encontrado na IDWALL')
            return ie

    def checa_plano(self, loja):
        plano = None
        try:
            plano = DashboardPlanos.objects.get(id=loja.plano_id)
            return plano
        except DashboardPlanos.DoesNotFound:
            return plano

    def checa_produto(self, descricao):
        produto = None
        try:
            produto = DashboardProdutos.objects.get(descricao=descricao)
            return {'num_protheus': produto.num_protheus, 'valor_unit': produto.valor_unit, 'qtd': produto.qtd}
        # except DashboardProdutos.DoesNotFound:
        #    pass
        except Exception as e:
            print(str(e))
        return produto

    def get_produto(self, prod, utencilios=False, produtos=None):
        if not utencilios:
            return self.checa_produto(prod['maquina'])
        else:
            return self.checa_produto(str(produtos.descricao).upper())

    def depara_produto(self, produtos):
        # função de depara TEMPORÁRIA Para comparar produtos cadastrados no Protheus com os do integrador
        __prods = {
            'INGENICO MOVE/5000': {'num_protheus': '800004', 'valor_unit': '688.88', 'qtd': '1'},
            'C680': {'num_protheus': '800005', 'valor_unit': ' 838.80', 'qtd': '1'},
            'VX 680': {'num_protheus': '800015', 'valor_unit': '231', 'qtd': '1'},
            'VX685': {'num_protheus': '800003', 'valor_unit': '590.00', 'qtd': '1'},
            'CHIP VIVO': {'num_protheus': '800010', 'valor_unit': '0.01', 'qtd': '1'},
            'CHIP TIM': {'num_protheus': '800011', 'valor_unit': '0.01', 'qtd': '1'},
            'CHIP OI': {'num_protheus': '800012', 'valor_unit': '0.01', 'qtd': '1'},
            'CHIP CLARO': {'num_protheus': '800013', 'valor_unit': '0.01', 'qtd': '1'},
            'BOBINA': {'num_protheus': '800014', 'valor_unit': '1.21', 'qtd': '6'},
        }

        return __prods[str(produtos.descricao).upper()]

    def consulta_ie_idwall(self, numero):
        headers = {
            'Authorization': settings.TOKEN_IDWALL,
            'Content-Type': "application/json"
        }
        endpoint = settings.URL_IDWALL + \
            "/relatorios/" + str(numero) + "/dados"
        response = requests.request("GET", endpoint, headers=headers)
        return response

    def remove_acentos(self, text):
        try:
            text = unicode(text, 'utf-8')
        except (TypeError, NameError):
            pass
        text = unicodedata.normalize('NFD', text)
        text = text.encode('ascii', 'ignore')
        text = text.decode("utf-8")
        return str(text).replace("'", "").upper().strip()

    def trata_cep(self, text):
        return str(text).replace('-', '')

    def trata_ie(self, ie):
        return str(''.join(c for c in ie if c not in punctuation)).replace('-', '').strip()
