from api.models.pendentes import Pendentes
from datetime import datetime
import logging

def send_pendente(lojista, fase, sub_fase, pendencias):
    try:
        pendente = Pendentes.objects.filter(lojista=lojista).first()
        if not pendente:
            pendente = Pendentes(lojista=lojista)
            pendente.pendencias = {}

        pendente.fase = fase
        if not pendente.pendencias.get(fase):
            pendente.pendencias[fase] = {sub_fase: pendencias}
        pendente.pendencias[fase][sub_fase] = pendencias
        pendente.updated_at = datetime.now()
        pendente.save()
    except Exception as err:
        logging.error(err)

def disable_pendente(lojista, fase):
    try:
        pendente = Pendentes.objects.filter(lojista=lojista).first()
        if not pendente:
            return

        pendente.fase = fase
        if pendente.pendencias.get(fase):
            del pendente.pendencias[fase]
        pendente.save()
    except Exception as err:
        logging.error(err)