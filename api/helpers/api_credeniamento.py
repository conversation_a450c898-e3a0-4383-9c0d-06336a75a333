import os
from concurrent.futures import ThreadPoolExecutor

import requests
from fiscal.log_service import Logger

logger = Logger()

class ApiCredenciamentoManager:
    def __init__(self):
        self.url  = os.environ.get('LUCREE_API')
        self.token = self.get_token()

    def get_token(self):
        try:
            response = requests.post(f'{self.url}autenticacao/v1', json={
                "username": os.environ.get('LUCREE_API_AUTENTICACAO_USERNAME'),
                "password": os.environ.get('LUCREE_API_AUTENTICACAO_PASSWORD')
            })
            response.raise_for_status()
            return response.json()['id_token']
        except Exception as e:
            logger.registrar(f"Erro ao obter token: {e}", "CRITICAL")
            return None

    def atualizar_servico_externos(self, lojista_id: int):
        """Atualiza os serviços externos para o lojista especificado."""
        servicos_externos = [
            ("CERC", "/cerc/send", int),
            ("OCTADESK", "/octadesk/send", int),
            ("RDSTATION", "/rdstation/send", int)
        ]
        def atualizar_servico(servico):
            """Função auxiliar para atualizar um serviço específico."""
            url = f"{self.url}credenciamento{servico[1]}"
            headers = {
                'Authorization': f'{self.token}',
                'Content-Type': 'application/json'
            }
            payload = {
                "id": servico[2](lojista_id)
            }
            try:
                response = requests.post(url, headers=headers, json=payload)
                response.raise_for_status()
                logger.registrar(f"Serviço {servico[0]} atualizado com sucesso!", "INFO")

            except requests.exceptions.HTTPError as e:
                logger.registrar(f"Erro ao atualizar serviço {servico[0]}: {e}", "ERROR")
            except Exception as e:
                logger.registrar(f"Erro ao atualizar serviço {servico[0]}: {e}", "CRITICAL")
        with ThreadPoolExecutor(max_workers=3) as executor:
            executor.map(atualizar_servico, servicos_externos)


if __name__ == "__main__":
    manager = ApiCredenciamentoManager()
    manager.atualizar_servico_externos(22249)
