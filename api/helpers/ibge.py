import requests
from django.conf import settings


class Ibge:
    def __init__(self, validadores=None):
        self.url = settings.URL_IBGE
        self.estados = None
        self.estado = None
        self.cidades = None
        self.cidade = None
        self.validadores = validadores

    def get_ufs_ibge(self):
        r = requests.get(self.url+'estados')
        if r.status_code == 200:
            valid = True
            self.estados = r.json()
        else:
            valid = False

        return valid

    def search_in_ufs(self, uf):
        valid = False
        try:
            self.estado = next(
                item for item in self.estados if item['sigla'] == uf)
            valid = True
            return valid
        except StopIteration:
            return valid

    def get_cidades_por_estado_ibge(self, id_uf):
        r = requests.get(self.url + 'estados/'+str(id_uf)+'/municipios')
        if r.status_code == 200:
            cidades_valid = True
            self.cidades = r.json()
        else:
            cidades_valid = False
        return cidades_valid

    def search_in_cidades(self, nome_cidade):
        valid = False
        validador = self.validadores
        try:
            nome = validador.remove_acentos(nome_cidade)
            self.cidade = next(
                item for item in self.cidades if validador.remove_acentos(item['nome']) == nome)
            valid = True
            return valid
        except StopIteration:
            return valid
