import requests
import json
from rest_framework import status
from django.conf import settings

from api.models.general_models import DashboardLojista


class Omie:

    def __init__(self, tipo):

        self.appKey = settings.OMIE_APP_KEY
        self.appSecret = settings.OMIE_APP_SECRET

        if tipo == "cliente":
            self.url = settings.OMIE_URL + "/geral/clientes/"
            self.call = "IncluirCliente"
        else:
            self.url = settings.OMIE_URL + "/produtos/pedido/"
            self.call = "IncluirPedido"

    def createCliente(self, body):

        headers = {'Content-Type': 'application/json'}
        data = json.dumps({"call": self.call, "app_secret": self.appSecret,
                           "app_key": self.appKey, "param": [body]})

        try:
            results = requests.post(
                self.url, headers=headers, data=data)
        except Exception as e:
            print(str(e))

        cliente = None
        resultado_text = results.text
        try:
            valido, resultado = self.trataresultado(results.json())
            if valido is False:
                # CASO CLIENTE JA TENHA SIDO CADASTRADO MAS NAO TEM NO NOSSO BD
                lojista = DashboardLojista.objects.filter(
                    documento=body["cnpj_cpf"],
                    status_idwall='CONCLUIDO',
                    resultado_idwall='VALID'
                ).exclude(
                    matriz_idwall__isnull=True,
                    numero_idwall__isnull=True,
                    nome=''
                ).first()
                req = {
                    "pagina": 1,
                    "registros_por_pagina": 50,
                    "apenas_importado_api": "N",
                    "clientesFiltro": {
                        "cnpj_cpf": lojista.documento
                    }
                }
                dataCliente = json.dumps({"call": "ListarClientesResumido", "app_secret": self.appSecret,
                                          "app_key": self.appKey, "param": [req]})
                resultsCli = requests.post(
                    "https://app.omie.com.br/api/v1/geral/clientes/", headers=headers, data=dataCliente)
                status_chamada = status.HTTP_201_CREATED
                mensagem = None
                cliente = resultsCli.json(
                )["clientes_cadastro_resumido"][0]["codigo_cliente"]
            else:
                status_chamada = status.HTTP_201_CREATED
                cliente = resultado['codigo_cliente_omie']
                mensagem = resultado['descricao_status']
        except TypeError:
            mensagem = json.loads(resultado_text)
            status_chamada = status.HTTP_400_BAD_REQUEST
            return status_chamada, mensagem, None
        except ValueError:
            mensagem = resultado_text
            status_chamada = status.HTTP_500_INTERNAL_SERVER_ERROR
            return status_chamada, mensagem, None

        return status_chamada, mensagem, cliente

    def createPedido(self, body):
        headers = {'Content-Type': 'application/json'}
        data = json.dumps({"call": self.call, "app_secret": self.appSecret,
                           "app_key": self.appKey, "param": [body]})

        try:
            results = requests.post(
                self.url, headers=headers, data=data)
        except Exception as e:
            print(str(e))

        pedido = None
        resultado_text = results.text
        print(results.json())
        try:
            valido, resultado = self.trataresultado(results.json())
            if valido is False:
                status_chamada = status.HTTP_400_BAD_REQUEST
                mensagem = resultado['faultstring']
                pedido = None
            else:
                status_chamada = status.HTTP_201_CREATED
                pedido = resultado['codigo_pedido']
                mensagem = resultado['descricao_status']
        except TypeError:
            mensagem = json.loads(resultado_text)
            status_chamada = status.HTTP_400_BAD_REQUEST
            return status_chamada, mensagem, None
        except ValueError:
            mensagem = resultado_text
            status_chamada = status.HTTP_500_INTERNAL_SERVER_ERROR
            return status_chamada, mensagem, None
        except Exception as err:
            print(e)
        return status_chamada, mensagem, pedido

    def trataresultado(self, resultadochamada):
        valido = False
        resultado = None

        if 'codigo_status' in resultadochamada:
            if resultadochamada['codigo_status'] != '0':
                if 'descricao_status' in resultadochamada:
                    resultado = resultadochamada
            else:
                valido = True
                resultado = resultadochamada
        return valido, resultado
