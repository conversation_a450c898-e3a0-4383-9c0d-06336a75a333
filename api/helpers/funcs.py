from datetime import datetime
import json


class Funcs:
    @staticmethod
    def atualiza_fase(fases_list, fases_concluidas, proxima_fase: str):
        for fase in fases_list:
            # Paulo Camacan LUC-618
            # se a fase for a proxima, em_andamento
            if fase["fase"] == proxima_fase:
                fase["status"] = 'em_andamento'
                continue
            
            for index, concluida in enumerate(fases_concluidas):
                if fase["fase"] == concluida:
                    # Remove a fase que já foi modificada
                    fases_concluidas.pop(index)
                    # modifica a fase
                    fase['status'] = 'concluida'
                    fase['dt_alteracao'] = str(datetime.now())
                    break
                
        return json.dumps(fases_list)

    @staticmethod
    def desabilita_fase(fases_list, fases_desabilitar):
        fases_list = json.loads(fases_list)
        for fase_desabilitar in fases_desabilitar:
            for fase in fases_list:
                if fase['fase'] == fase_desabilitar:
                    fase['status'] = 'nao_considerar'
        return json.dumps(fases_list)

    @staticmethod
    def fase_atual(fases_list):
        try:
            fases = json.loads(fases_list)
            for d in fases:
                if d['status'] == 'em_andamento':
                    return d['fase']
        except Exception as e:
            print(str(e))
