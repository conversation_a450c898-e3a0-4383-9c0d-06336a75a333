from api.models.general_models import DashboardCnpj, DashboardCpf, DashboardProdutos, DashboardSintegraitem
from ..exceptions import IbgeExceptions
from ..models import DashboardEstados, DashboardCidades, DashboardEnderecolojista
import datetime
import time
from django.conf import settings

class MontaChamada:
    def __init__(self, body=None, lojista=None, endereco=None, responsavel=None, validadores=None):
        self.body = body
        self.lojista = lojista
        self.endereco = endereco
        self.responsavel = responsavel
        self.validadores = validadores

    # Gustavo Nicolau - LUC-1568
    # monta o json para criar cliente no OMIE
    def montagem_clientes_omie(self):
        endereco = DashboardEnderecolojista.objects.filter(
            lojista=self.lojista, tipo='1').first()
        integra = DashboardSintegraitem.objects.filter(
            lojista=self.lojista).first()
        razao = ""
        if self.lojista.matriz_idwall == "lucree_QSA_pf":
            cpf = DashboardCpf.objects.filter(
                lojista=self.lojista.id
            ).first()
            razao = cpf.nome
        else:
            cnpj = DashboardCnpj.objects.filter(
                lojista=self.lojista.id
            ).first()
            razao = cnpj.nome_empresarial
        if integra:
            ie = integra.inscricao_estadual
            opcao_simples = str(integra.opcao_simples)[0]
        else:
            ie = None
            opcao_simples = None
        return {
            "codigo_cliente_integracao": self.lojista.id,
            "email": "<EMAIL>",
            "razao_social": str(razao[:60]),
            "nome_fantasia": self.lojista.nome,
            "cnpj_cpf": self.lojista.documento,
            "inscricao_estadual": ie,
            "optante_simples_nacional": opcao_simples,
            "endereco": endereco.logradouro,
            "endereco_numero": endereco.numero,
            "bairro": endereco.bairro,
            "complemento": endereco.complemento,
            "estado": endereco.estado,
            "cidade": endereco.cidade,
            "cep": endereco.cep,
            "codigo_pais": 1058
        }

    # Gustavo Nicolau - LUC-1568
    # monta o json para criar Pedido no OMIE

    def montagem_pedidos_omie(self, cliente, transportadora):
        year =  datetime.datetime.now().year
        data = "31/12/" + str(year)

        prods = DashboardProdutos.objects.filter(
            descricao=self.body["maquina"]
        ).first()
        milliseconds = int(round(time.time() * 1000))
        print(prods.descricao)
        nome = prods.descricao.lower()
        endereco = DashboardEnderecolojista.objects.filter(
            lojista=self.lojista, tipo='1').first()
        cfop = 0
        quantidade = 1
        if nome.lower().find("venda") > -1:

            ie, tipo_consumidor = self.validadores.checa_isencao(
                self.body["cpf_cnpj"])
            ie = 'ISENTO' if ie == 'ERRO_IDWALL' else ie
            if ie == "ISENTO":
                tes = "503"
                cfop = "6108"
            else:
                if endereco.estado in ["MG", "RS", "MT"]:
                    tes = "505"
                    cfop = "6403"
                elif endereco.estado == "SP":
                    cfop = "5102"
                else:
                    tes = "502"
                    cfop = "6102"
        else:
            if not nome.find("bobina") > -1:
                if endereco.estado == "SP":
                    cfop = 5908
                else:
                    cfop = 6908
            else:
                quantidade = 6
                if endereco.estado == "SP":
                    cfop = 5910
                else:
                    cfop = 6910

        return {
            "cabecalho": {
                "codigo_cliente": cliente,
                "codigo_pedido_integracao": str(cliente) + str(milliseconds),
                "data_previsao": str(data),
                "etapa": "10",
                "codigo_parcela": "000"
            },
            "det": [
                {
                    "ide": {
                        "codigo_item_integracao": prods.num_omie,
                    },
                    "produto": {
                        "codigo_produto": prods.num_omie,
                        "quantidade": quantidade,
                        "cfop": cfop,
                        "valor_unitario": prods.valor_unit
                    }
                }
            ],
            "frete": {
                "modalidade": "0"
            },
            "informacoes_adicionais": {
                "codigo_categoria": "1.01.03",
                "codigo_conta_corrente": settings.CONTA_CORRENTE_OMIE,
                "consumidor_final": "S",
                "enviar_email": "N"
            }
        }

    def montagem_clientes(self):
        ie, tipo_consumidor = self.validadores.checa_isencao(
            self.body["cpf_cnpj"])
        return {
            "empresa": "03",
            "filial": "01",
            "Object": [
                {
                    "campo": "A1_LOJA",
                    "conteudo": "01"    # valor fixo
                },
                {
                    "campo": "A1_NOME",
                    # nome do lojista - integrador (IDWALL)
                    "conteudo": self.validadores.remove_acentos(self.lojista.nome)[0:30]
                },
                {
                    "campo": "A1_PESSOA",
                    # tipo de pessoa - (matriz_idwall) [Juridica/física]
                    "conteudo":  self.validadores.define_tipo(self.validadores.get_matriz(self.body))
                },
                {
                    "campo": "A1_END",
                    # endereço integrador
                    "conteudo": self.validadores.remove_acentos(self.endereco.logradouro) + ", " + (str(self.endereco.numero) or 'sn')
                },
                {
                    "campo": "A1_COMPLEM",
                    # endereço integrador
                    "conteudo": self.validadores.remove_acentos(self.endereco.complemento)
                },
                {
                    "campo": "A1_NREDUZ",
                    # nome do lojista - integrador (IDWALL)
                    "conteudo": self.validadores.remove_acentos(self.lojista.nome)[0:30]
                },
                {
                    "campo": "A1_TIPO",
                    # F - CONSUMIDOR FINAL/ # R - REVENDEDOR (ISENTO NA INSCRIÇÃO ESTADUAL .SE TIVER I.E É REVENDEDOR)
                    "conteudo": tipo_consumidor
                },
                {
                    "campo": "A1_EST",
                    "conteudo": self.endereco.estado    # integrador tabela endereço
                },
                {
                    "campo": "A1_MUN",
                    # integrador tabela endereço (TRATAR CARACTERE ESPECIAL/UPPERCASE)
                    "conteudo": self.validadores.remove_acentos(self.endereco.cidade)
                },
                {
                    "campo": "A1_CGC",
                    # integrador tabela lojista (UNICO DADO INSERIDO VIA API)
                    "conteudo": self.body["cpf_cnpj"]
                },
                {
                    # integrador tabela endereço (TRATAR CARACTERE ESPECIAL/UPPERCASE)
                    "campo": "A1_BAIRRO",
                    "conteudo": self.validadores.remove_acentos(self.endereco.bairro)
                },
                {
                    "campo": "A1_COD_MUN",
                    # api do IBGE
                    "conteudo": self.consulta_cidade_ibge(self.endereco.estado, self.endereco.cidade, self.validadores)
                },
                {
                    "campo": "A1_CEP",
                    # integrador tabela endereço
                    "conteudo": self.validadores.trata_cep(self.endereco.cep)
                },
                {
                    "campo": "A1_DDD",
                    "conteudo": self.responsavel.ddd        # integrador tabela de responsavel
                },
                {
                    "campo": "A1_TEL",
                    "conteudo": self.responsavel.telefone       # integrador tabela de responsavel
                },
                {
                    "campo": "A1_INSCR",
                    # se for pessoa física é isento / se for pj e tiver inscrição estadual joga aqui se não é isento
                    "conteudo": 'ISENTO' if not ie or ie == 'ERRO_IDWALL' else ie
                },
                {
                    "campo": "A1_NATUREZ",
                    "conteudo": "111401"
                },
                {
                    "campo": "A1_HPAGE",
                    "conteudo": 'ISENTO' if not ie else ie
                },
                {
                    "campo": "A1_CONTRIB",
                    "conteudo": '2' if (not ie or ie == 'ISENTO' or ie == 'ERRO_IDWALL') else '1'
                }
            ]

        }

    def montagem_pedidos(self, cliente, utensilios=False, produtos=None, transportadora=None):

        if not utensilios:
            _prods = self.validadores.get_produto(self.body)
        else:
            _prods = self.validadores.get_produto(
                self.lojista, utensilios, produtos)

        # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
        # Ajustando TES e CFOP para planos com venda de máquina
        # Por Renato Aloi 10/01/2020
        endereco = DashboardEnderecolojista.objects.filter(
            lojista=self.lojista, tipo='1').first()
        tes = '511' if utensilios and _prods['num_protheus'] == '800014' else '509'
        cfop = "5102"
        if self.lojista.plano.tipo_contratacao == 'VENDA':
            if endereco.estado == "SP":
                tes = "502"
            else:
                ie, tipo_consumidor = self.validadores.checa_isencao(
                    self.body["cpf_cnpj"])
                ie = 'ISENTO' if ie == 'ERRO_IDWALL' else ie
                if ie == "ISENTO":
                    tes = "503"
                    cfop = "6108"
                else:
                    if endereco.estado in ["MG", "RS", "MT"]:
                        tes = "505"
                        cfop = "6403"
                    else:
                        tes = "502"
                        cfop = "6102"

        # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #

        if self.lojista.plano.tipo_contratacao == 'VENDA':
            return {
                "empresa": "03",
                "filial": "01",
                "Cabeca": [
                    {
                        "campo": "C5_TIPO",
                        "conteudo": "N"     # sempre normal (N)
                    },
                    {
                        "campo": "C5_CLIENTE",
                        "conteudo": cliente
                    },
                    {
                        "campo": "C5_LOJACLI",
                        "conteudo": "01"    # SEMPRE 01
                    },
                    {
                        "campo": "C5_CLIENT",
                        "conteudo": cliente
                    },
                    {
                        "campo": "C5_LOJAENT",
                        "conteudo": "01"
                    },
                    {
                        "campo": "C5_CONDPAG",
                        "conteudo": "150"
                    },
                    {
                        "campo": "C5_NATUREZ",
                        "conteudo": "111401"
                    },
                    {
                        "campo": "C5_MENNOTA",
                        "conteudo": "PEDIDO GERADO VIA INTEGRADOR"  # NÃO É OBRIGATÓRIO
                    },
                    {
                        "campo": "C5_TRANSP",
                        # TIPO DE TRANSPORTADORA
                        "conteudo": "02" if utensilios else transportadora if transportadora in ["01", "02"] else ""
                    },
                    {
                        "campo": "C5_TPFRETE",
                        # TIPO DE FRETE
                        "conteudo": "C" if utensilios else "C" if transportadora in ["01", "02"] else "S"
                    },
                    {
                        "campo": "C5_XPEDLUC",
                        "conteudo": str(self.lojista.id)  # ID DO CLIENTE
                    },
                    {
                        "campo": "C5_XOPERA",
                        "conteudo": ''  # TIPO DE OPERADORA
                    }
                ],
                "Itens": [
                    [
                        {
                            "campo": "C6_ITEM",
                            "conteudo": "01"
                        },
                        {
                            "campo": "C6_PRODUTO",
                            "conteudo": _prods['num_protheus']
                        },
                        {
                            "campo": "C6_UM",
                            "conteudo": "UN"
                        },
                        {
                            "campo": "C6_QTDVEN",
                            "conteudo": str(self.lojista.num_terminais) if not utensilios else str(_prods['qtd'])
                        },
                        {
                            "campo": "C6_PRCVEN",
                            "conteudo": str(_prods['valor_unit'])
                        },
                        {
                            "campo": "C6_PRUNIT",
                            "conteudo": str(_prods['valor_unit'])
                        },
                        {
                            "campo": "C6_TES",
                            "conteudo": tes
                        },
                        {
                            "campo": "C6_CF",
                            "conteudo": cfop
                        }
                    ]
                ]
            }

        return {
            "empresa": "03",
            "filial": "01",
            "Cabeca": [
                {
                    "campo": "C5_TIPO",
                    "conteudo": "N"     # sempre normal (N)
                },
                {
                    "campo": "C5_CLIENTE",
                    "conteudo": cliente
                },
                {
                    "campo": "C5_LOJACLI",
                    "conteudo": "01"    # SEMPRE 01
                },
                {
                    "campo": "C5_CLIENT",
                    "conteudo": cliente
                },
                {
                    "campo": "C5_LOJAENT",
                    "conteudo": "01"
                },
                {
                    "campo": "C5_CONDPAG",
                    "conteudo": "150"
                },
                {
                    "campo": "C5_NATUREZ",
                    "conteudo": "111401"
                },
                {
                    "campo": "C5_MENNOTA",
                    "conteudo": "PEDIDO GERADO VIA INTEGRADOR"  # NÃO É OBRIGATÓRIO
                },
                {
                    "campo": "C5_TRANSP",
                    # TIPO DE TRANSPORTADORA
                    "conteudo": "02" if utensilios else transportadora if transportadora in ["01", "02"] else ""
                },
                {
                    "campo": "C5_TPFRETE",
                    # TIPO DE FRETE
                    "conteudo": "C" if utensilios else "C" if transportadora in ["01", "02"] else "S"
                },
                {
                    "campo": "C5_XPEDLUC",
                    "conteudo": str(self.lojista.id)  # ID DO CLIENTE
                },
                {
                    "campo": "C5_XOPERA",
                    "conteudo": ''  # TIPO DE OPERADORA
                }
            ],
            "Itens": [
                [
                    {
                        "campo": "C6_ITEM",
                        "conteudo": "01"
                    },
                    {
                        "campo": "C6_PRODUTO",
                        "conteudo": _prods['num_protheus']
                    },
                    {
                        "campo": "C6_UM",
                        "conteudo": "UN"
                    },
                    {
                        "campo": "C6_QTDVEN",
                        "conteudo": str(self.lojista.num_terminais) if not utensilios else str(_prods['qtd'])
                    },
                    {
                        "campo": "C6_PRCVEN",
                        "conteudo": str(_prods['valor_unit'])
                    },
                    {
                        "campo": "C6_PRUNIT",
                        "conteudo": str(_prods['valor_unit'])
                    },
                    {
                        "campo": "C6_TES",
                        "conteudo": '511' if utensilios and _prods['num_protheus'] == '800014' else '509'
                    }
                ]
            ]
        }

    def define_codigo_operadora(self):
        if self.lojista.operadora.id == 1:
            return 'VIVO'
        elif self.lojista.operadora.id == 2:
            return 'TIM'
        elif self.lojista.operadora.id == 3:
            return 'OI'
        elif self.lojista.operadora.id == 4:
            return 'CLARO'

    def consulta_cidade_ibge(self, uf, cidade_alvo, validador):
        try:
            estado = DashboardEstados.objects.get(sigla=uf)
            nome = validador.remove_acentos(cidade_alvo)
            cidades = DashboardCidades.objects.filter(estado=estado)
            cidade = next(
                item for item in cidades if validador.remove_acentos(item.nome) == nome)
            return str(cidade.id)[2:]
        except DashboardEstados.DoesNotExists:
            raise IbgeExceptions('Estado não encontrado na base do IBGE')
