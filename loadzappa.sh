
err (){
    echo "$(tput bold && tput setaf 1): $1 $(tput sgr0)"
}

main(){
    if [[ ! `jq --help` ]]; then
        err "Instale o jq: sudo apt install jq"
        return
    fi

    if [[ ! -e "$(pwd)/zappa_settings.json" ]]; then
        err "Nao existe zappa_settings.json nessa pasta"
        return
    fi

    load=`cat "$(pwd)/zappa_settings.json"`
    if [[ -z "$load" ]]; then
        err "ZappaSettings esta vazio"
        return
    fi

    [[ -e "$(pwd)/.env" ]] && [[ `rm "$(pwd)/.env"` ]] 

    echo $load | jq ."$1".environment_variables | jq -r 'to_entries[] | (.key) + "=" + "\"" + (.value) + "\""' >> "$(pwd)/.env"
}

main $1
