# Base Lambda

Repositório base para a criação de APIs _Serverless_ na AWS utilizando o framework [Zappa](https://github.com/Miserlou/Zappa).

## Modelo de arquitetura

Em uma arquitetura Serverless, cada API é separada por contextos bem definidos em funções Lambda na AWS, que são acionadas através de um API Gateway. O nosso modelo de arquitetura segue este padrão:

![](imgs/example.jpg)

## Pré-requisitos

Para a utilização do __Zappa__, é necessário:

  - Separar seus projetos em _virtualenvs_ (_venv, virtualenv, virtualenvwrapper,_ etc...)
  - Instalar o Zappa via _pip_ neste virtualenv.
  - Possuir o arquivo de credenciais da AWS configurado.

## Utilizando virtualenvs

Existem várias formas de utilizar um virtualenv, inclusive pela distribuição padrão do Python (`python -m venv .venv`). Para os próximos exemplos, utilizarei o [virtualenvwrapper](https://virtualenvwrapper.readthedocs.io/en/latest/).

## Credenciais

Esse passo é necessário para executar os comandos do Zappa localmente (`tail`, `deploy` e etc), mas não necessariamente para realizar o deploy com o fluxo de CI/CD (será explicado em breve).

Defina as credenciais no arquivo _credentials_ da AWS:

- ~/.aws/credentials no Linux, macOS, ou Unix

- C:\Users\<USER>\\.aws\credentials no Windows

O arquivo deve conter:

```
[default]
aws_access_key_id = your_access_key_id
aws_secret_access_key = your_secret_access_key
```

Para mais informações: https://docs.aws.amazon.com/pt_br/sdk-for-java/v1/developer-guide/setup-credentials.html

## Setup

Clone o repositório _base\_lambda_, crie seu virtualenv e instale o Zappa:

```
cd test_api/
git clone https://<EMAIL>/bevipag/lambda_base.git .
rm -rf .git imgs README.md  # este repositório é só a base para o seu repositório final :)
mkvirtualenv test
pip install -r requirements.txt # ou pip install zappa

```

__OBS:__ O nome do virtualenv precisa ser diferente do nome da aplicação !

Instale as dependências necessárias para o projeto (`Django`, `Flask`, ou o que for necessário) e salve-as no requirements.txt do seu projeto:

```
pip install django
pip freeze > requirements.txt
```

Crie sua aplicação e faça as alterações que forem necessárias no arquivo [zappa_settings.json](zappa_settings.json). Abaixo, segue a explicação para os campos que estamos utilizando atualmente em nossas APIs:

```
{
  "hml": { # Estágio da sua aplicação
      "aws_region": "us-west-2", # Região da AWS utilizada
      "django_settings": "liquidacao.settings", # Caminho do settings utilizado
      "project_name": "liquidacao", # Nome do projeto (o nome da API na console do API Gateway será "project_name-estágio da aplicação". Ex liquidacao-hml)
      "runtime": "python3.6",
      "s3_bucket": "lucree-liquidacao-hml", # Nome do bukect s3 utilizado pelo Zappa
      "apigateway_description": "API de Liquidacao da Lucree - HML", # Descrição da API (fica visível na console do API Gateway)
      "lambda_description": "API de Liquidacao da Lucree - HML", # Descrição da função Lambda (fica visível na console do API Gateway)
      "memory_size": 180, # Quantidade de memória utilizada pela aplicação
      "log_level": "DEBUG", # Serveridade de log
      "cors": true, # Habilitar ou não o CORS
      "keep_warm": false, # Será explicado abaixo
      "domain": "api-hml.lucree.com.br", # Nome do domínio criado (API Gateway -> Custom Domain Names). Utilizamos este para APIs de homologação
      "base_path": "liquidacao", # Caminho base da aplicação (explicação abaixo)
      "endpoint_configuration": [
        "REGIONAL" # Opção escolhida para homologação por conta de limitações/custos da AWS
      ],
      "route53_enabled": false, # Utilizamos o DNS da Azure, então mantemos false
      "certificate_arn": "arn:aws:acm:us-east-1:175572419266:certificate/05725bab-8c5b-406f-a18d-cd26e4c9cfe8", # Identificador do certificado registrado para o nosso domínio público
      "environment_variables": { # Variáveis de ambiente disponíveis no ambiente do Lambda
          "RDS_DB_NAME": "subadiqdb1",
          "RDS_USERNAME": "subadiqdb",
          "RDS_PASSWORD": "SubAd1q32f",
          "RDS_HOSTNAME": "transacsubadiq.cxmg4sq9azs9.us-west-2.rds.amazonaws.com"
          # As variáveis abaixo serão explicadas na sessão "Swagger"
          "AWS_ACCESS_KEY_ID": "********************",
          "AWS_SECRET_ACCESS_KEY": "/lMP6k3z9Nfs8j18Snv0K0Vo+MISkaeIutWWI2Jc",
          "S3_BUCKET": "swagger-static",
          "AWS_S3_REGION_NAME": "us-west-2"
      },
      "exclude": [ # Arquivos que não precisam ser levados para a nossa aplicação
          "*.md",
          "Dockerfile",
          ".*",
          "zappa_settings.json",
          "setup.cfg",
          "docker-*",
          "bitbucket-*",
          "ci.sh"
      ],
      "vpc_config": { # Em qual Subnets e SGs o lambda fará parte
          "SubnetIds": [
                  "subnet-069b4cc8898ec7708",
                  "subnet-01a2db17a3e4cc521",
                  "subnet-0426a8959dc215581"
              ],
          "SecurityGroupIds": [ "sg-57861622" ]
      },
      "authorizer": { # Integração com o Cognito para gestão de usuários. Caso sua API não precise de autenticação, remover a clausula authorizer inteira
        "type": "COGNITO_USER_POOLS",
        "provider_arns": [ # Identificador do User Pool do Cognito à ser utilizado
          "arn:aws:cognito-idp:us-west-2:175572419266:userpool/us-west-2_axDl475s7"
        ]
      }
  },
  "prod": { # Estágio da aplicação
      "extends": "hml", # Estende todas as configurações do estágio hml, substituindo o que for reescrito
      "lambda_description": "API de Liquidacao da Lucree - Prod",
      "apigateway_description": "API de Liquidacao da Lucree - Prod",
      "s3_bucket": "lucree-liquidacao-prod",
      "domain": "api.lucree.com.br", # Domínio configurado para produção
      "endpoint_configuration":[
        "EDGE" # Padrão para APIs produtivas
      ],
      "log_level": "INFO",
      "keep_warm": true,
      "keep_warm_expression": "rate(5 minutes)"
  }
}

```

Existem outras [configurações adicionais](https://github.com/Miserlou/Zappa#advanced-settings) que podem ser acrescentadas de acordo com a necessidade do seu projeto.

Caso queira realizar uma __configuração do 0__ da sua aplicação, é só remover o settings atual e executar:

```
zappa init
```

## CI/CD

Estamos utilizando o padrão [Trunk Base Development](https://paulhammant.com/2013/04/05/what-is-trunk-based-development/) para realizar o fluxo de desenvolvimento.

Com o Bitbucket Pipelines, o deploy é realizado automaticamente no _estágio_ de __homologação__, assim que um `git push` é feito para a branch `master`. Para realizar o deploy em __produção__ é necessário _"taguear"_ a versão da release que será implementada.

Antes de tudo, para o pipeline funcionar, é necessário cadastrar as credenciais do usuário __deployer__ no repositório do Bitbucket (_Settings_ -> _Repository variables_):

![](imgs/credenciais.png)


__OBS:__ Colete as credenciais em algum repositório que já possua o bitbucket pipelines configurado (Ex: lucree-liquidacao).

Os arquivos responsáveis pelo fluxo do pipeline são o [bitbucket-pipelines.yml](bitbucket-pipelines.yml) e o [ci.sh](ci.sh). Caso haja alguma dúvida, consulte-os.

Após isso, para realizar o deploy em homologação:


```
git commit -m 'Testando feature xpto'
git push origin master # o pipeline é ativado e o deploy é realizado automaticamente em hml
```

![](imgs/hml.png)

Para realizar o deploy em produção, é necessário escolher o commit da alteração que precisa subir para produção e enviar para o Bitbucket:

```
git log
```

![](imgs/hash_commit.png)

![](imgs/tag_release.png)

__OBS: Sempre__ utilizar o padrão release-<num_versao>.

![](imgs/tag_commit.png)

E pronto, um novo build/deploy é iniciado em produção com a versão de release especificada:

![](imgs/deploys.png)

## Deploy, Update, Rollback (manual)...

Caso queira realizar o deploy ou fazer outras ações manualmente, sem utilizar o pipeline de CI/CD, siga os passos a seguir:

Após realizar todas as alterações necessárias no arquivo de settings do Zappa:

```
zappa deploy hml
Deploying..
Your application is now live at: https://7k6anj0k99.execute-api.us-east-1.amazonaws.com/production
```

Pronto ! Sua API está disponível no API Gateway da AWS com a URL customizada acima:

![](imgs/apis.png)

Para mapear o base_path da sua aplicação no domínio especificado (ou seja, para expor sua API com o nome de domínio registrado):

```
zappa certify hml --yes
Calling certify for stage hml..
Certifying domain api-hml.lucree.com.br..
Updating domain name!
Certificate updated!
```

A rota será criada no domínio conforme abaixo:

![](imgs/base_path.png)

Agora sua API está acessível através do endereço público na rota especificada em base_path !

Para atualizar sua aplicação:

```
zappa update hml
```

Para ver o status:

```
zappa status hml
```

É possível realizar o rollback para uma versão específica anterior (os números de versões são visualizados pelo zappa status):

```
zappa rollback hml -n 3
```

Para ver as logs (existem vários parâmetros possíveis para [filtrar/visualizar logs](https://github.com/Miserlou/Zappa#tailing-logs)):

```
zappa tail hml
```

Para realizar o undeploy:

```
zappa undeploy hml
```

Para os demais comandos/parâmetros, verificar a [documentação oficial](https://github.com/Miserlou/Zappa).

## Jobs Agendados

É possível realizar o deploy de Jobs (tarefas agendadas, assíncronas e etc) que não necessariamente são APIs REST (Ex: uma função Python que atualiza uma tabela de hora em hora, uma aplicação que remove arquivos de um S3 e etc). Para isto, verificar a parte de [scheduling](https://github.com/Miserlou/Zappa#scheduling) da documentação oficial.

### Keep Warm

Como aplicações em Lambda só são inicializadas quando uma requisição é feita, as primeiras chamadas podem apresentar uma certa lentidão. A opção `"keep_warm": true` mantém as APIs "aquecidas", realizando requisições para estas de acordo com o tempo especificado em `"keep_warm_expression": "rate(5 minutes)"` (devemos manter essa opção ativa em produção !).

### Base Path

O base path da sua aplicação será o contexto inicial dela, ou seja será o seu "/". Sendo assim, se eu possuo as rotas abaixo:

```
# Exemplo de rotas em Flask

@app.route('/test/1', methods=['GET'])
def test():
    return jsonify({'Return': 'from test'})

@app.route('/hello', methods=['GET'])
def hello():
    return jsonify({'Return': 'from hello'})

@app.route('/', methods=['GET'])
def root():
    return jsonify({'Return': 'from root'})
```

E o base_path no settings do Zappa estiver como __"hello"__, então:

- __GET__ para api.lucree.com.br/hello -> `Flask route '/'` -> `{'Return': 'from root'}`
- __GET__ para api.lucree.com.br/hello/hello -> ` Flask route '/hello'` -> `{'Return': 'from hello'}`
- __GET__ para api.lucree.com.br/hello/test/1 -> ` Flask route '/test/1'` -> `{'Return': 'from test'}`


## Swagger (Opcional)

Caso você queira documentar sua API em um endpoint utilizando o Swagger, utilize as variáveis de ambiente descritas acima na sessão _Setup_ que explica o arquivo de settings do Zappa (a partir da variável _AWS\_ACCESS\_KEY\_ID_), caso contrário, pode remove-las do arquivo sem nenhum problema.

Além de utilizá-las no arquivo de settings, é necessário adicionar os seguintes valores no seu arquivo `settings.py` da sua aplicação Django:

```
import os

# Static Config
S3_BUCKET = os.environ.get('S3_BUCKET')
AWS_S3_REGION_NAME = os.environ.get('AWS_S3_REGION_NAME')
AWS_ACCESS_KEY_ID = os.environ.get('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')
STATICFILES_STORAGE = "django_s3_storage.storage.StaticS3Storage"
AWS_STORAGE_BUCKET_NAME = S3_BUCKET
AWS_S3_BUCKET_NAME_STATIC = S3_BUCKET
AWS_S3_CUSTOM_DOMAIN = '%s.s3.us-west-2.amazonaws.com' % S3_BUCKET
STATIC_URL = "https://%s/" % AWS_S3_CUSTOM_DOMAIN
AWS_DEFAULT_ACL = None
```

E também, instalar a dependência do Django para trabalhar com S3:

```
pip install django-s3-storage
pip freeze > requirements.txt
```

```
INSTALLED_APPS = (
          ...,
          'django_s3_storage',
     )
```

Com isto, o Django utilizará o bucket S3 configurado no settings do Zappa para armazenar/acessar os estáticos utilizados pelo Swagger.

O padrão de endpoint que estamos adotando para expor a documentação da API pelo Swagger é o `'/'`. Ou seja:

```
https://api-hml.lucree.com.br/credenciamento -> Swagger sem autenticação do Cognito
https://api-hml.lucree.com.br/credenciamento/endpointqualquer/ -> API com autenticação do Cognito (caso sua app precise de autenticação)
```

### IMPORTANTE

Caso você esteja utilizando o Cognito para autenticação, assim que o deploy for realizado pelo pipeline de CI/CD, é necessário ir na console do API Gateway e remover a autenticação do endpoint `'/'` da sua API para expor o Swagger sem autenticação (este é o único step manual que ainda não está automatizado pelo pipeline de CI/CD). Ex:

Remoção do Cognito do endpoint `'/'`:

![](imgs/remove_cog.png)

Agora a sessão _Authorization_ está como _None_:

![](imgs/without_cog.png)

Após isso, é necessário clicar em "Deploy API" para persistir as alterações:

![](imgs/deploy_api.png)

Escolha o estágio e pronto. O Swagger estará exposto sem autenticação no base_path da sua aplicação:

![](imgs/deploy_api2.png)

Os demais endpoints permanecerão inalterados:

![](imgs/with_cog.png)

![](imgs/with_cog2.png)


__OBS:__ Caso haja alguma dúvida, consultar as APIs de liquidacao ou credenciamento !
